<template>
  <el-dialog
    v-model="dialogVisible"
    width="493px"
    top="30vh"
    class="msgdialog"
    :title="tname"
    @close="close"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }"
  >
    <img class="msgicon" :src="imgUrl[messageType]" alt="" />
    <div class="msgcss">{{ message }}</div>
  </el-dialog>
</template>

<script setup>
import { reactive } from "vue"
import errorimg from '@/assets/errorimg.png';

const dialogVisible = ref(false)
const messageType = ref('success')
const tname = ref('')
const message = ref('')
const imgUrl = reactive({
    success:errorimg,
    error:errorimg
})
const closeAction = ref(null)
function show({ type, title, msg, onClose }) {
    messageType.value = type
    console.log('sssssss',title)
    tname.value = title
    message.value = msg
    closeAction.value = onClose
    dialogVisible.value = true
}
function close() {
    dialogVisible.value = false
    if (closeAction.value) {
        closeAction()
    }
}
defineExpose({
  show,
  close
})
</script>
<style lang="scss">
.msgdialog {
  .el-dialog {
    border-radius: 8px !important;
    
  }
  .el-dialog__header{
      border-bottom: 1px solid #E1E4EB ;
      padding: 16px 30px;
  }
  .el-dialog__body{
    text-align: center !important;
    padding-bottom: 30px !important;
  }
    .el-dialog__headerbtn{
        width: 24px !important;
        height: 24px !important;
        font-size: 20px !important;
        right: 30px;
        top: 16px;
        .el-icon{
            width: 24px;
            height: 24px;
            color: #2D2F33 !important;
        }
    }
    
}
</style>
<style lang="scss" scoped>

.msgicon {
  width: 64px;
  height: 64px;
  margin: 0 auto !important;
  margin-top: 20px !important;
}
.titlename {
  margin-top: 16px;
  height: 30px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #333333;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.msgcss {
  margin-top: 24px;
  height: 27px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 18px;
  color: #44474D;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 84px;
}
</style>
